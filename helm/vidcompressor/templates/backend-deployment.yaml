apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-backend
spec:
  replicas: {{ .Values.backend.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}-backend
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}-backend
    spec:
      containers:
        - name: backend
          image: "{{ .Values.backend.image.repository }}:{{ .Values.backend.image.tag }}"
          ports:
            - containerPort: {{ .Values.backend.service.targetPort }}
          env:
            - name: GoogleCloud__ProjectId
              value: "{{ .Values.backend.env.googleCloud.projectId }}"
            - name: GoogleCloud__Region
              value: "{{ .Values.backend.env.googleCloud.region }}"
            - name: GoogleCloud__InputBucketName
              value: "{{ .Values.backend.env.googleCloud.inputBucket }}"
            - name: GoogleCloud__OutputBucketName
              value: "{{ .Values.backend.env.googleCloud.outputBucket }}"
            - name: GoogleCloud__ServiceAccountKeyPath
              value: "{{ .Values.backend.env.googleCloud.serviceAccountKeyPath }}"
            - name: GoogleCloud__Transcoder__Location
              value: "{{ .Values.backend.env.googleCloud.transcoderLocation }}"
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: "{{ .Values.backend.env.googleCloud.serviceAccountKeyPath }}"
          volumeMounts:
            - name: transcoder-service-account
              mountPath: /secrets
              readOnly: true
      volumes:
        - name: transcoder-service-account
          secret:
            secretName: transcoder-service-account
