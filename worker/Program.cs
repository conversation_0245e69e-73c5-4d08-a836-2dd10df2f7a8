using OpenTelemetry.Trace;
using OpenTelemetry.Resources;
using StackExchange.Redis;
using worker;
using VidCompressor.Services;
using CasCap.Models;

var builder = Host.CreateApplicationBuilder(args);

builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
{
    var configuration = sp.GetRequiredService<IConfiguration>();
    var redisConnectionString = configuration.GetConnectionString("Redis") ?? "localhost";
    return ConnectionMultiplexer.Connect(redisConnectionString);
});

builder.Services.AddOpenTelemetry()
    .WithTracing(tracerProviderBuilder =>
        tracerProviderBuilder
            .AddConsoleExporter()
            .AddSource("VidCompressor.Worker")
            .SetResourceBuilder(
                ResourceBuilder.CreateDefault()
                    .AddService(serviceName: "VidCompressor.Worker", serviceVersion: "1.0.0"))
            .AddHttpClientInstrumentation()
            .AddAspNetCoreInstrumentation()
            .AddRedisInstrumentation());

builder.Services.AddHttpClient();

// Configure GooglePhotosOptions (you'll need to add configuration)
builder.Services.Configure<GooglePhotosOptions>(options =>
{
    // Configure your Google Photos options here
    // You may want to read these from configuration
});

builder.Services.AddSingleton<GooglePhotosService>();
builder.Services.AddHostedService<Worker>();

var host = builder.Build();
host.Run();
