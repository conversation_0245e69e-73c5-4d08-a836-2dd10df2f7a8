#!/bin/bash

echo "🚀 Starting VidCompressor Full Docker Environment"

# Build and start all services
echo "🔨 Building and starting all services..."
docker-compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check service status
echo "🔍 Checking service status..."
docker-compose ps

echo ""
echo "📋 Service logs (press Ctrl+C to exit):"
docker-compose logs -f

echo ""
echo "🌐 Your services are available at:"
echo "   - Backend API: http://localhost:5000"
echo "   - PostgreSQL: localhost:5432"
echo "   - Redis: localhost:6379"
echo ""
echo "🛑 To stop all services:"
echo "   docker-compose down"
