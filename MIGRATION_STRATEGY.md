# Migration Strategy: FFmpeg Worker to Google Transcoder API

## Overview

This document outlines the migration strategy from the current FFmpeg-based worker architecture to the new Google Transcoder API with Cloud Storage integration.

## Current Architecture

- **Backend API**: Handles user requests and queues compression jobs via Redis
- **Worker Service**: Separate service that processes compression jobs using FFmpeg
- **Redis**: Message broker for job queuing
- **Google Photos API**: For downloading and uploading videos

## New Architecture

- **Backend API**: Handles user requests and includes integrated background service
- **Google Cloud Storage**: Temporary storage for video processing
- **Google Transcoder API**: Cloud-based video compression
- **Redis**: Still used for job queuing but simplified
- **PostgreSQL**: Enhanced with compression job tracking

## Migration Steps

### Phase 1: Infrastructure Setup ✅
- [x] Add Google Cloud Storage buckets to Terraform
- [x] Add Google Transcoder API resources
- [x] Configure service accounts and IAM permissions

### Phase 2: Backend Integration ✅
- [x] Create new models for compression jobs
- [x] Implement Google Cloud Storage service
- [x] Implement Google Transcoder API service
- [x] Add database migration for compression jobs
- [x] Update API endpoints with new workflow
- [x] Create integrated background service

### Phase 3: Frontend Updates ✅
- [x] Add compression settings modal
- [x] Add overwrite original option
- [x] Update compression request handling

### Phase 4: Deployment Strategy (Current)
- [ ] Update Helm charts to remove worker deployment
- [ ] Update environment variables and secrets
- [ ] Deploy new backend with integrated background service
- [ ] Test end-to-end compression workflow

### Phase 5: Cleanup
- [ ] Remove worker project files
- [ ] Update documentation
- [ ] Monitor and optimize performance

## Key Changes

### Removed Components
- **Worker Service**: No longer needed as background processing is integrated into backend
- **FFmpeg Dependencies**: Replaced with Google Transcoder API
- **Complex Redis Messaging**: Simplified to basic job queuing

### New Components
- **Google Cloud Storage Integration**: For temporary video storage
- **Google Transcoder API**: For cloud-based video compression
- **Enhanced Database Schema**: For comprehensive job tracking
- **Integrated Background Service**: Built into the main backend application

### Benefits
1. **Simplified Architecture**: Fewer moving parts and services to manage
2. **Cloud-Native**: Leverages Google Cloud's managed services
3. **Better Scalability**: Google Transcoder API handles scaling automatically
4. **Improved Reliability**: Managed services reduce infrastructure complexity
5. **Cost Optimization**: Pay-per-use model for transcoding
6. **Better Monitoring**: Centralized logging and job tracking

## Configuration Changes

### Environment Variables
```bash
# New Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_REGION=us-central1
GOOGLE_CLOUD_INPUT_BUCKET=your-project-id-vidcompressor-input
GOOGLE_CLOUD_OUTPUT_BUCKET=your-project-id-vidcompressor-output
GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY_PATH=/secrets/transcoder-service-account-key

# Existing Configuration (unchanged)
GOOGLE_CLIENT_ID=...
GOOGLE_CLIENT_SECRET=...
JWT_SECRET=...
POSTGRES_CONNECTION_STRING=...
REDIS_CONNECTION_STRING=...
```

### Kubernetes Secrets
```yaml
# New secret for Google Cloud service account
apiVersion: v1
kind: Secret
metadata:
  name: transcoder-service-account
type: Opaque
data:
  key.json: <base64-encoded-service-account-key>
```

## Testing Strategy

### Unit Tests
- [ ] Test Google Cloud Storage service
- [ ] Test Google Transcoder API service
- [ ] Test compression job workflow

### Integration Tests
- [ ] End-to-end compression workflow
- [ ] Error handling and recovery
- [ ] Job status tracking and updates

### Performance Tests
- [ ] Concurrent compression jobs
- [ ] Large file handling
- [ ] Storage cleanup verification

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: Revert to previous Helm chart version
2. **Database Rollback**: Run migration rollback if needed
3. **Configuration Rollback**: Restore previous environment variables
4. **Service Restoration**: Redeploy worker service if necessary

## Monitoring and Alerts

### New Metrics to Monitor
- Google Transcoder API usage and costs
- Cloud Storage usage and costs
- Compression job success/failure rates
- Job processing times
- Storage cleanup effectiveness

### Alerts to Configure
- Failed compression jobs
- High Cloud Storage usage
- Transcoder API errors
- Long-running jobs (potential stuck jobs)

## Cost Considerations

### Google Transcoder API Pricing
- Pay-per-minute of video processed
- Different rates for different output qualities
- Monitor usage to optimize costs

### Cloud Storage Costs
- Temporary storage for input/output videos
- Lifecycle policies to automatically delete old files
- Monitor storage usage and cleanup effectiveness

## Security Considerations

### Service Account Permissions
- Minimal required permissions for Transcoder API
- Separate service account for storage operations
- Regular rotation of service account keys

### Data Protection
- Temporary storage with automatic cleanup
- Encryption at rest and in transit
- Audit logging for all operations

## Timeline

- **Week 1**: Complete infrastructure setup and testing
- **Week 2**: Deploy and test new backend service
- **Week 3**: Frontend updates and user testing
- **Week 4**: Production deployment and monitoring
- **Week 5**: Cleanup and documentation

## Success Criteria

- [ ] All compression jobs process successfully
- [ ] No increase in processing time compared to FFmpeg
- [ ] Cost per compression is within acceptable range
- [ ] Zero data loss during migration
- [ ] User experience is maintained or improved
