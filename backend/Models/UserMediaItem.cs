using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace VidCompressor.Models;

/// <summary>
/// Represents a media item (photo or video) that a user has loaded from Google Photos.
/// This provides persistent storage so media items remain available across page refreshes.
/// </summary>
public class UserMediaItem
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    /// <summary>
    /// The Google Photos media item ID
    /// </summary>
    [Required]
    public string GoogleMediaItemId { get; set; } = string.Empty;
    
    /// <summary>
    /// Original filename from Google Photos
    /// </summary>
    [Required]
    public string Filename { get; set; } = string.Empty;
    
    /// <summary>
    /// MIME type of the media item (e.g., image/jpeg, video/mp4)
    /// </summary>
    [Required]
    public string MimeType { get; set; } = string.Empty;
    
    /// <summary>
    /// Base URL from PhotosPicker API for downloading the media item
    /// </summary>
    [Required]
    public string BaseUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// Type of media (photo or video)
    /// </summary>
    [Required]
    public MediaType MediaType { get; set; }
    
    /// <summary>
    /// Media width in pixels
    /// </summary>
    public int? Width { get; set; }
    
    /// <summary>
    /// Media height in pixels
    /// </summary>
    public int? Height { get; set; }

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long? FileSizeBytes { get; set; }

    /// <summary>
    /// Creation time from Google Photos metadata
    /// </summary>
    public DateTime? CreationTime { get; set; }

    /// <summary>
    /// Google Photos URL for this media item (productUrl from API)
    /// </summary>
    public string? GooglePhotosUrl { get; set; }

    /// <summary>
    /// Additional metadata from Google Photos (stored as JSON)
    /// </summary>
    public string? Metadata { get; set; }
    
    /// <summary>
    /// When this media item was added to the user's collection
    /// </summary>
    public DateTime AddedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// When this media item was last accessed/viewed
    /// </summary>
    public DateTime LastAccessedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public User? User { get; set; }
}

/// <summary>
/// DTO for creating/updating user media items
/// </summary>
public class UserMediaItemRequest
{
    [Required]
    public string GoogleMediaItemId { get; set; } = string.Empty;
    
    [Required]
    public string Filename { get; set; } = string.Empty;
    
    [Required]
    public string MimeType { get; set; } = string.Empty;
    
    [Required]
    public string BaseUrl { get; set; } = string.Empty;
    
    [Required]
    public MediaType MediaType { get; set; }
    
    public int? Width { get; set; }
    public int? Height { get; set; }
    public long? FileSizeBytes { get; set; }
    public DateTime? CreationTime { get; set; }
    public string? Metadata { get; set; }
}

/// <summary>
/// DTO for returning user media items with compression job status
/// </summary>
public class UserMediaItemResponse
{
    public string Id { get; set; } = string.Empty;
    public string GoogleMediaItemId { get; set; } = string.Empty;
    public string Filename { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = string.Empty;
    public MediaType MediaType { get; set; }
    public int? Width { get; set; }
    public int? Height { get; set; }
    public long? FileSizeBytes { get; set; }
    public DateTime? CreationTime { get; set; }
    public string? Metadata { get; set; }
    public DateTime AddedAt { get; set; }
    public DateTime LastAccessedAt { get; set; }
    
    /// <summary>
    /// Google Photos URL for this media item (productUrl from API)
    /// </summary>
    public string? GooglePhotosUrl { get; set; }

    /// <summary>
    /// Latest compression job for this media item (if any)
    /// </summary>
    public CompressionJobSummary? LatestCompressionJob { get; set; }
}

/// <summary>
/// Summary of compression job information for media item responses
/// </summary>
public class CompressionJobSummary
{
    public string Id { get; set; } = string.Empty;
    public CompressionJobStatus Status { get; set; }
    public string Quality { get; set; } = string.Empty;
    public bool UploadToGooglePhotos { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public long? OriginalSizeBytes { get; set; }
    public long? CompressedSizeBytes { get; set; }
    public double? CompressionRatio { get; set; }
}
