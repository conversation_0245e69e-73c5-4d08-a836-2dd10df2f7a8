using System.ComponentModel.DataAnnotations;

namespace VidCompressor.Models;

/// <summary>
/// Configurable credit costs for different operations
/// </summary>
public class CreditCost
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// Type of operation (PhotoCompression, VideoCompressionSD, VideoCompressionHD, VideoCompression4K)
    /// </summary>
    [Required]
    public string OperationType { get; set; } = string.Empty;
    
    /// <summary>
    /// Cost in credits
    /// </summary>
    [Required]
    public int Cost { get; set; }
    
    /// <summary>
    /// Unit for the cost (e.g., "per_item" for photos, "per_minute" for videos)
    /// </summary>
    [Required]
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// Human-readable description
    /// </summary>
    [Required]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether this cost configuration is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// When this cost configuration was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// When this cost configuration was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Standard operation types for credit costs
/// </summary>
public static class CreditCostOperationTypes
{
    public const string PhotoCompression = "PhotoCompression";
    public const string VideoCompressionSD = "VideoCompressionSD";
    public const string VideoCompressionHD = "VideoCompressionHD";
    public const string VideoCompression4K = "VideoCompression4K";
}
