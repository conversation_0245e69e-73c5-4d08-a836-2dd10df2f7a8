using System.ComponentModel.DataAnnotations;

namespace VidCompressor.Models;

/// <summary>
/// Represents a credit transaction (usage, purchase, refund, etc.)
/// </summary>
public class CreditTransaction
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    /// <summary>
    /// Type of transaction (Usage, Purchase, Refund, AdminAdjustment)
    /// </summary>
    [Required]
    public CreditTransactionType Type { get; set; }
    
    /// <summary>
    /// Amount of credits (positive for additions, negative for usage)
    /// </summary>
    [Required]
    public int Amount { get; set; }
    
    /// <summary>
    /// User's credit balance after this transaction
    /// </summary>
    [Required]
    public int BalanceAfter { get; set; }
    
    /// <summary>
    /// Description of the transaction
    /// </summary>
    [Required]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Related compression job ID (if applicable)
    /// </summary>
    public string? CompressionJobId { get; set; }
    
    /// <summary>
    /// Related payment/purchase ID (if applicable)
    /// </summary>
    public string? PaymentId { get; set; }
    
    /// <summary>
    /// When the transaction occurred
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public User? User { get; set; }
    public CompressionJob? CompressionJob { get; set; }
}

public enum CreditTransactionType
{
    /// <summary>
    /// Credits used for compression
    /// </summary>
    Usage,
    
    /// <summary>
    /// Credits purchased by user
    /// </summary>
    Purchase,
    
    /// <summary>
    /// Credits refunded (e.g., failed job)
    /// </summary>
    Refund,
    
    /// <summary>
    /// Manual adjustment by admin
    /// </summary>
    AdminAdjustment,
    
    /// <summary>
    /// Initial credits given to new users
    /// </summary>
    Welcome
}
