@backend_HostAddress = http://localhost:5119

GET {{backend_HostAddress}}/weatherforecast/
Accept: application/json

###

# Google Photos Picker API Endpoints

# 1. Create a new picker session (initiate video selection)
GET {{backend_HostAddress}}/api/videos
Accept: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###

# 2. Check session status (poll until mediaItemsSet is true)
GET {{backend_HostAddress}}/api/videos/session/SESSION_ID_HERE/status
Accept: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###

# 3. Get selected videos from the session
GET {{backend_HostAddress}}/api/videos/session/SESSION_ID_HERE/videos
Accept: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###

# 4. Get selected videos with pagination
GET {{backend_HostAddress}}/api/videos/session/SESSION_ID_HERE/videos?pageSize=10&pageToken=NEXT_PAGE_TOKEN
Accept: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###

# 5. Clean up session (delete when done)
DELETE {{backend_HostAddress}}/api/videos/session/SESSION_ID_HERE
Accept: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###
