using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using VidCompressor.Models;
using System.Net;

namespace VidCompressor.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EmailSignupController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EmailSignupController> _logger;

        public EmailSignupController(ApplicationDbContext context, ILogger<EmailSignupController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost]
        public async Task<ActionResult<EmailSignupResponse>> SignupEmail([FromBody] EmailSignupRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new EmailSignupResponse
                    {
                        Success = false,
                        Message = "Please provide a valid email address."
                    });
                }

                // Normalize email
                var normalizedEmail = request.Email.Trim().ToLowerInvariant();

                // Check if email already exists
                var existingSignup = await _context.EmailSignups
                    .FirstOrDefaultAsync(e => e.Email == normalizedEmail);

                if (existingSignup != null)
                {
                    return Ok(new EmailSignupResponse
                    {
                        Success = true,
                        Message = "You're already on our waitlist! We'll notify you when we launch."
                    });
                }

                // Get client information
                var ipAddress = GetClientIpAddress();
                var userAgent = Request.Headers["User-Agent"].ToString();

                // Create new email signup
                var emailSignup = new EmailSignup
                {
                    Email = normalizedEmail,
                    CreatedAt = DateTime.UtcNow,
                    IpAddress = ipAddress,
                    UserAgent = userAgent
                };

                _context.EmailSignups.Add(emailSignup);
                await _context.SaveChangesAsync();

                _logger.LogInformation("New email signup: {Email} from IP: {IpAddress}", 
                    normalizedEmail, ipAddress);

                return Ok(new EmailSignupResponse
                {
                    Success = true,
                    Message = "Thanks for signing up! You'll be notified when we launch with your 100 free credits."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing email signup for: {Email}", request.Email);
                
                return StatusCode(500, new EmailSignupResponse
                {
                    Success = false,
                    Message = "Something went wrong. Please try again later."
                });
            }
        }

        [HttpGet("count")]
        public async Task<ActionResult<object>> GetSignupCount()
        {
            try
            {
                var count = await _context.EmailSignups.CountAsync();
                return Ok(new { count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting signup count");
                return StatusCode(500, new { error = "Unable to get signup count" });
            }
        }

        [HttpGet("list")]
        [Microsoft.AspNetCore.Authorization.Authorize] // Only authenticated users can see the list
        public async Task<ActionResult<object>> GetSignupList(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 50)
        {
            try
            {
                if (pageSize > 100) pageSize = 100; // Limit page size
                if (page < 1) page = 1;

                var skip = (page - 1) * pageSize;

                var signups = await _context.EmailSignups
                    .OrderByDescending(e => e.CreatedAt)
                    .Skip(skip)
                    .Take(pageSize)
                    .Select(e => new
                    {
                        e.Id,
                        e.Email,
                        e.CreatedAt,
                        e.IsNotified,
                        e.NotifiedAt
                    })
                    .ToListAsync();

                var totalCount = await _context.EmailSignups.CountAsync();

                return Ok(new
                {
                    signups,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting signup list");
                return StatusCode(500, new { error = "Unable to get signup list" });
            }
        }

        private string GetClientIpAddress()
        {
            try
            {
                // Check for forwarded IP first (in case of proxy/load balancer)
                var forwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
                if (!string.IsNullOrEmpty(forwardedFor))
                {
                    var ips = forwardedFor.Split(',');
                    if (ips.Length > 0)
                    {
                        return ips[0].Trim();
                    }
                }

                // Check for real IP header
                var realIp = Request.Headers["X-Real-IP"].FirstOrDefault();
                if (!string.IsNullOrEmpty(realIp))
                {
                    return realIp;
                }

                // Fall back to connection remote IP
                return Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }
}
