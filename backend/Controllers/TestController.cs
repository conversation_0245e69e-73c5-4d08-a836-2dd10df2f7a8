using Microsoft.AspNetCore.Mvc;
using Google.Cloud.Storage.V1;
using Google.Cloud.Tasks.V2;

namespace VidCompressor.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<TestController> _logger;

        public TestController(IConfiguration configuration, ILogger<TestController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        [HttpGet("cloud-storage")]
        public async Task<IActionResult> TestCloudStorage()
        {
            try
            {
                var projectId = _configuration["GoogleCloud:ProjectId"];
                var inputBucket = _configuration["GoogleCloud:InputBucketName"];
                var outputBucket = _configuration["GoogleCloud:OutputBucketName"];
                var tempBucket = _configuration["GoogleCloud:TempBucketName"];

                var client = StorageClient.Create();

                // Test bucket access
                var buckets = new List<object>();

                // Test input bucket
                try
                {
                    var inputBucketInfo = await client.GetBucketAsync(inputBucket);
                    buckets.Add(new { Name = inputBucket, Status = "✅ Accessible", Location = inputBucketInfo.Location });
                }
                catch (Exception ex)
                {
                    buckets.Add(new { Name = inputBucket, Status = "❌ Error", Error = ex.Message });
                }

                // Test output bucket
                try
                {
                    var outputBucketInfo = await client.GetBucketAsync(outputBucket);
                    buckets.Add(new { Name = outputBucket, Status = "✅ Accessible", Location = outputBucketInfo.Location });
                }
                catch (Exception ex)
                {
                    buckets.Add(new { Name = outputBucket, Status = "❌ Error", Error = ex.Message });
                }

                // Test temp bucket
                try
                {
                    var tempBucketInfo = await client.GetBucketAsync(tempBucket);
                    buckets.Add(new { Name = tempBucket, Status = "✅ Accessible", Location = tempBucketInfo.Location });
                }
                catch (Exception ex)
                {
                    buckets.Add(new { Name = tempBucket, Status = "❌ Error", Error = ex.Message });
                }

                return Ok(new
                {
                    Message = "Cloud Storage Test Results",
                    ProjectId = projectId,
                    Buckets = buckets,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing cloud storage");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpGet("cloud-tasks")]
        public async Task<IActionResult> TestCloudTasks()
        {
            try
            {
                var projectId = _configuration["GoogleCloud:ProjectId"];
                var location = _configuration["GoogleCloud:Region"];
                var queueName = _configuration["GoogleCloud:CloudTasks:QueueName"];

                var client = CloudTasksClient.Create();
                var queuePath = new QueueName(projectId, location, queueName);

                // Test queue access
                var queue = await client.GetQueueAsync(queuePath);

                return Ok(new
                {
                    Message = "Cloud Tasks Test Results",
                    QueueName = queue.Name,
                    State = queue.State.ToString(),
                    RateLimits = new
                    {
                        MaxConcurrentDispatches = queue.RateLimits?.MaxConcurrentDispatches,
                        MaxDispatchesPerSecond = queue.RateLimits?.MaxDispatchesPerSecond
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing cloud tasks");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpPost("upload-test")]
        public async Task<IActionResult> TestFileUpload()
        {
            try
            {
                var inputBucket = _configuration["GoogleCloud:InputBucketName"];
                var client = StorageClient.Create();

                // Create a test file
                var testContent = $"Test file created at {DateTime.UtcNow}";
                var testFileName = $"test-{DateTime.UtcNow:yyyyMMdd-HHmmss}.txt";

                using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(testContent));
                
                // Upload test file
                var obj = await client.UploadObjectAsync(inputBucket, testFileName, "text/plain", stream);

                return Ok(new
                {
                    Message = "File upload test successful",
                    FileName = testFileName,
                    Bucket = inputBucket,
                    Size = obj.Size,
                    Url = $"gs://{inputBucket}/{testFileName}",
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing file upload");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpGet("transcoder")]
        public IActionResult TestTranscoder()
        {
            try
            {
                var projectId = _configuration["GoogleCloud:ProjectId"];
                var location = _configuration["GoogleCloud:Region"];

                // Test if we can access the Transcoder API
                var client = Google.Cloud.Video.Transcoder.V1.TranscoderServiceClient.Create();
                var parent = $"projects/{projectId}/locations/{location}";

                // Just test if we can list job templates (this doesn't create anything)
                var templates = client.ListJobTemplates(parent);

                return Ok(new
                {
                    Message = "Transcoder API Test Results",
                    ProjectId = projectId,
                    Location = location,
                    Status = "✅ API Accessible",
                    TemplateCount = templates.Count(),
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing transcoder");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpGet("config")]
        public IActionResult GetConfiguration()
        {
            return Ok(new
            {
                GoogleCloud = new
                {
                    ProjectId = _configuration["GoogleCloud:ProjectId"],
                    Region = _configuration["GoogleCloud:Region"],
                    InputBucketName = _configuration["GoogleCloud:InputBucketName"],
                    OutputBucketName = _configuration["GoogleCloud:OutputBucketName"],
                    TempBucketName = _configuration["GoogleCloud:TempBucketName"],
                    CloudTasks = new
                    {
                        ProjectId = _configuration["GoogleCloud:CloudTasks:ProjectId"],
                        Location = _configuration["GoogleCloud:CloudTasks:Location"],
                        QueueName = _configuration["GoogleCloud:CloudTasks:QueueName"],
                        HandlerUrl = _configuration["GoogleCloud:CloudTasks:HandlerUrl"]
                    }
                },
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                Timestamp = DateTime.UtcNow
            });
        }
    }
}
