FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy project files for restore
COPY backend/*.csproj ./backend/
COPY shared/*.csproj ./shared/

# Restore dependencies
WORKDIR /app/backend
RUN dotnet restore

# Copy source code
WORKDIR /app
COPY backend/ ./backend/
COPY shared/ ./shared/

# Build and publish
WORKDIR /app/backend
RUN dotnet publish -c Release -o out

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /app/backend/out .

# <PERSON> Run expects the app to listen on the PORT environment variable
EXPOSE 8080
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

ENTRYPOINT ["dotnet", "backend.dll"]
