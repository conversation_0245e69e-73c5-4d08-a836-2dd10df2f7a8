﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class AddCompressionJobsAndUserPreferences : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "DefaultOverwriteOriginal",
                table: "Users",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "CompressionJobs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    MediaItemId = table.Column<string>(type: "text", nullable: false),
                    Quality = table.Column<string>(type: "text", nullable: false),
                    OverwriteOriginal = table.Column<bool>(type: "boolean", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    InputStoragePath = table.Column<string>(type: "text", nullable: true),
                    OutputStoragePath = table.Column<string>(type: "text", nullable: true),
                    TranscoderJobName = table.Column<string>(type: "text", nullable: true),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    StartedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    OriginalSizeBytes = table.Column<long>(type: "bigint", nullable: true),
                    CompressedSizeBytes = table.Column<long>(type: "bigint", nullable: true),
                    CompressionRatio = table.Column<double>(type: "double precision", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompressionJobs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompressionJobs_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CompressionJobs_CreatedAt",
                table: "CompressionJobs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CompressionJobs_Status",
                table: "CompressionJobs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_CompressionJobs_UserId",
                table: "CompressionJobs",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CompressionJobs");

            migrationBuilder.DropColumn(
                name: "DefaultOverwriteOriginal",
                table: "Users");
        }
    }
}
