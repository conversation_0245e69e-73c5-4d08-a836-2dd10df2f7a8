﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class AddGooglePhotosUrls : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "GooglePhotosUrl",
                table: "UserMediaItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompressedGooglePhotosUrl",
                table: "CompressionJobs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GooglePhotosUrl",
                table: "CompressionJobs",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GooglePhotosUrl",
                table: "UserMediaItems");

            migrationBuilder.DropColumn(
                name: "CompressedGooglePhotosUrl",
                table: "CompressionJobs");

            migrationBuilder.DropColumn(
                name: "GooglePhotosUrl",
                table: "CompressionJobs");
        }
    }
}
