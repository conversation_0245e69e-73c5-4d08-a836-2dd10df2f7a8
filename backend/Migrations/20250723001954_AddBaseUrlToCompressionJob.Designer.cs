﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace backend.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250723001954_AddBaseUrlToCompressionJob")]
    partial class AddBaseUrlToCompressionJob
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("VidCompressor.Models.CompressionJob", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("BaseUrl")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CompressedSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<double?>("CompressionRatio")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<string>("InputStoragePath")
                        .HasColumnType("text");

                    b.Property<string>("MediaItemId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("OriginalSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<string>("OutputStoragePath")
                        .HasColumnType("text");

                    b.Property<bool>("OverwriteOriginal")
                        .HasColumnType("boolean");

                    b.Property<string>("Quality")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TranscoderJobName")
                        .HasColumnType("text");

                    b.Property<bool>("UploadToGooglePhotos")
                        .HasColumnType("boolean");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Status");

                    b.HasIndex("UserId");

                    b.ToTable("CompressionJobs");
                });

            modelBuilder.Entity("VidCompressor.Models.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<bool>("DefaultOverwriteOriginal")
                        .HasColumnType("boolean");

                    b.Property<bool>("DefaultUploadToGooglePhotos")
                        .HasColumnType("boolean");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GoogleAccessToken")
                        .HasColumnType("text");

                    b.Property<string>("GoogleRefreshToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("GoogleTokenExpiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SubscriptionStatus")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("VidCompressor.Models.CompressionJob", b =>
                {
                    b.HasOne("VidCompressor.Models.User", "User")
                        .WithMany("CompressionJobs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("VidCompressor.Models.User", b =>
                {
                    b.Navigation("CompressionJobs");
                });
#pragma warning restore 612, 618
        }
    }
}
