name: .NET CI

on:
  push:
    branches: [ "main" ]
    paths:
      - '**.cs'
      - '**.csproj'
  pull_request:
    branches: [ "main" ]
    paths:
      - '**.cs'
      - '**.csproj'

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x
    - name: Restore dependencies
      run: dotnet restore
    - name: Build
      run: dotnet build --no-restore
    - name: Test
      run: dotnet test --no-build --verbosity normal
