import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Avatar,
  Divider,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  AccountCircle,
  Logout,
  Add,
  CreditCard
} from '@mui/icons-material';
import BillingHistory from './BillingHistory';
import UsageHistory from './UsageHistory';

interface AccountScreenProps {
  userEmail: string;
  userCredits: number;
  creditsLoading: boolean;
  onLogout: () => void;
  token: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`account-tabpanel-${index}`}
      aria-labelledby={`account-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `account-tab-${index}`,
    'aria-controls': `account-tabpanel-${index}`,
  };
}

const AccountScreen: React.FC<AccountScreenProps> = ({
  userEmail,
  userCredits,
  creditsLoading,
  onLogout,
  token
}) => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Get initials from email for avatar
  const getInitials = (email: string) => {
    const name = email.split('@')[0];
    return name.charAt(0).toUpperCase();
  };

  const handleAddCredits = () => {
    // TODO: Navigate to add credits screen or open modal
    console.log('Add credits clicked');
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 400, mb: 1 }}>
          Account
        </Typography>
        <Typography variant="body1" sx={{ color: 'text.secondary' }}>
          Manage your profile, billing, and usage history
        </Typography>
      </Box>

      {/* Overview Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 3,
          border: 1,
          borderColor: 'divider',
          borderRadius: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar
            sx={{
              width: 64,
              height: 64,
              mr: 3,
              bgcolor: 'primary.main',
              fontSize: '1.5rem'
            }}
          >
            {getInitials(userEmail)}
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 500, mb: 0.5 }}>
              {userEmail}
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              User's Email
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 500, mb: 0.5 }}>
              Current Credit Balance
            </Typography>
            <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
              {creditsLoading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={24} />
                  <span style={{ fontSize: '1rem' }}>Loading...</span>
                </Box>
              ) : (
                `${userCredits} Credits`
              )}
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAddCredits}
            size="large"
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 500,
              px: 4,
              py: 1.5
            }}
          >
            Add More Credits
          </Button>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Button
          onClick={onLogout}
          startIcon={<Logout />}
          sx={{
            color: 'error.main',
            textTransform: 'none',
            fontWeight: 500,
            '&:hover': {
              backgroundColor: 'error.light',
              color: 'error.dark'
            },
            borderRadius: 1
          }}
        >
          Sign Out
        </Button>
      </Paper>

      {/* Tabs Section */}
      <Paper
        elevation={0}
        sx={{
          border: 1,
          borderColor: 'divider',
          borderRadius: 2
        }}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="account tabs"
            sx={{ px: 3 }}
          >
            <Tab
              label="Billing History"
              icon={<CreditCard />}
              iconPosition="start"
              sx={{ textTransform: 'none', fontWeight: 500 }}
              {...a11yProps(0)}
            />
            <Tab
              label="Usage History"
              icon={<AccountCircle />}
              iconPosition="start"
              sx={{ textTransform: 'none', fontWeight: 500 }}
              {...a11yProps(1)}
            />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <BillingHistory token={token} />
        </TabPanel>
        <TabPanel value={tabValue} index={1}>
          <UsageHistory token={token} />
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default AccountScreen;
