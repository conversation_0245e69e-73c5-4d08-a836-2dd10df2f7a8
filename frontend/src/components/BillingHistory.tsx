import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Download,
  Add,
  Remove
} from '@mui/icons-material';

interface BillingTransaction {
  id: string;
  type: string;
  amount: number;
  balanceAfter: number;
  description: string;
  compressionJobId?: string;
  paymentId?: string;
  createdAt: string;
}

interface BillingHistoryProps {
  token: string;
}

const BillingHistory: React.FC<BillingHistoryProps> = ({ token }) => {
  const [transactions, setTransactions] = useState<BillingTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTransactions();
  }, [token]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/credits/transactions?limit=100', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch transaction history');
      }

      const data = await response.json();
      // Filter for actual monetary transactions only (purchases with payment IDs)
      const billingTransactions = data.filter((t: BillingTransaction) =>
        t.type === 'Purchase' && t.paymentId
      );
      setTransactions(billingTransactions);
    } catch (err) {
      console.error('Error fetching billing history:', err);
      setError('Failed to load billing history');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatAmount = (amount: number) => {
    // For now, we'll show credit amounts. In a real app, this would be monetary amounts
    return `${amount} Credits`;
  };

  const getTransactionDescription = (transaction: BillingTransaction) => {
    if (transaction.paymentId) {
      // This would be a credit purchase
      return transaction.description || 'Credit Purchase';
    }
    return transaction.description;
  };

  const handleDownloadInvoice = (transaction: BillingTransaction) => {
    // TODO: Implement invoice download
    console.log('Download invoice for transaction:', transaction.id);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ px: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (transactions.length === 0) {
    return (
      <Box sx={{ px: 3, textAlign: 'center', py: 4 }}>
        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
          No billing history found
        </Typography>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          Credit purchases and payments will appear here
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ px: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 500 }}>
        Billing History
      </Typography>
      
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 600 }}>Date</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Amount</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Invoice</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {transactions.map((transaction) => (
              <TableRow key={transaction.id} hover>
                <TableCell>
                  {formatDate(transaction.createdAt)}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getTransactionDescription(transaction)}
                    {transaction.type === 'Purchase' && (
                      <Chip
                        label="Purchase"
                        size="small"
                        color="success"
                        variant="outlined"
                      />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {transaction.amount > 0 ? (
                      <Add sx={{ color: 'success.main', fontSize: 16 }} />
                    ) : (
                      <Remove sx={{ color: 'error.main', fontSize: 16 }} />
                    )}
                    <Typography
                      sx={{
                        color: transaction.amount > 0 ? 'success.main' : 'error.main',
                        fontWeight: 500
                      }}
                    >
                      {formatAmount(Math.abs(transaction.amount))}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  {transaction.paymentId ? (
                    <Button
                      size="small"
                      startIcon={<Download />}
                      onClick={() => handleDownloadInvoice(transaction)}
                      sx={{
                        textTransform: 'none',
                        fontWeight: 500
                      }}
                    >
                      Download
                    </Button>
                  ) : (
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      N/A
                    </Typography>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default BillingHistory;
