import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';

interface GoogleSignInButtonProps {
  onSuccess: (credential: string) => void;
  onError?: (error: any) => void;
  size?: 'large' | 'medium' | 'small';
  theme?: 'outline' | 'filled_blue' | 'filled_black';
  text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
  shape?: 'rectangular' | 'pill' | 'circle' | 'square';
  width?: number;
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          renderButton: (element: HTMLElement, config: any) => void;
          prompt: () => void;
        };
      };
    };
  }
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  onSuccess,
  onError,
  size = 'large',
  theme = 'outline',
  text = 'signin_with',
  shape = 'rectangular',
  width = 300
}) => {
  const buttonRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);

  useEffect(() => {
    const initializeGoogleSignIn = () => {
      if (!window.google || isInitialized.current) {
        return;
      }

      try {
        // Get client ID from environment or use the one from backend config
        const clientId = process.env.REACT_APP_GOOGLE_CLIENT_ID || '************-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com';

        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: (response: any) => {
            if (response.credential) {
              onSuccess(response.credential);
            } else if (onError) {
              onError(new Error('No credential received'));
            }
          },
          auto_select: false,
          cancel_on_tap_outside: true,
        });

        if (buttonRef.current) {
          window.google.accounts.id.renderButton(buttonRef.current, {
            type: 'standard',
            size: size,
            theme: theme,
            text: text,
            shape: shape,
            width: width,
          });
        }

        isInitialized.current = true;
      } catch (error) {
        console.error('Error initializing Google Sign-In:', error);
        if (onError) {
          onError(error);
        }
      }
    };

    // Check if Google Identity Services is already loaded
    if (window.google) {
      initializeGoogleSignIn();
    } else {
      // Wait for the script to load
      const checkGoogleLoaded = setInterval(() => {
        if (window.google) {
          clearInterval(checkGoogleLoaded);
          initializeGoogleSignIn();
        }
      }, 100);

      // Cleanup interval after 10 seconds
      setTimeout(() => {
        clearInterval(checkGoogleLoaded);
      }, 10000);

      return () => {
        clearInterval(checkGoogleLoaded);
      };
    }
  }, [onSuccess, onError, size, theme, text, shape, width]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <div ref={buttonRef} />
    </Box>
  );
};

export default GoogleSignInButton;
