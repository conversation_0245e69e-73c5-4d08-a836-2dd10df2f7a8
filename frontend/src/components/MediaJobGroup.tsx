import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  IconButton,
  Collapse,
  Avatar,
  Chip,
  Divider,
  List,
  ListItem,
  LinearProgress
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Photo,
  VideoLibrary,
  Close
} from '@mui/icons-material';
import JobItem from './JobItem';
import { JobData } from './JobsPanel';

interface MediaJobGroupProps {
  mediaItemId: string;
  jobs: JobData[];
  mediaItem?: {
    id: string;
    filename: string;
    mimeType: string;
    baseUrl: string;
  };
  token: string | null;
  onClearJob: (jobId: string) => void;
}

const MediaJobGroup: React.FC<MediaJobGroupProps> = ({
  mediaItemId,
  jobs,
  mediaItem,
  token,
  onClearJob
}) => {
  const [expanded, setExpanded] = useState(false);

  // Get the most recent job for the main display
  const latestJob = jobs[0];
  if (!latestJob) {
    return null; // Don't render if no jobs
  }

  const completedJobs = jobs.filter(job => job.status?.toLowerCase() === 'completed');
  const activeJobs = jobs.filter(job => job.status && !['completed', 'failed', 'cancelled'].includes(job.status.toLowerCase()));
  const hasActiveJobs = activeJobs.length > 0;
  const isLegacyJob = !latestJob.mediaItemId && !latestJob.filename && !latestJob.baseUrl;

  const getMediaPreview = () => {
    // Use the same preview endpoint as the gallery for consistency
    const mediaItemIdForPreview = mediaItem?.id || latestJob.mediaItemId;
    const baseUrl = mediaItem?.baseUrl || latestJob.baseUrl;

    if (mediaItemIdForPreview && token && baseUrl) {
      return (
        <Avatar
          src={`/api/videos/${mediaItemIdForPreview}/preview?width=60&height=60&crop=true&token=${encodeURIComponent(token)}&baseUrl=${encodeURIComponent(baseUrl)}`}
          sx={{
            width: 50,
            height: 50,
            borderRadius: 1,
            bgcolor: 'grey.200'
          }}
        >
          {latestJob.mediaType === 'Video' ? (
            <VideoLibrary sx={{ fontSize: 24, color: 'text.secondary' }} />
          ) : (
            <Photo sx={{ fontSize: 24, color: 'text.secondary' }} />
          )}
        </Avatar>
      );
    }

    return (
      <Avatar
        sx={{
          width: 50,
          height: 50,
          borderRadius: 1,
          bgcolor: 'grey.200'
        }}
      >
        {latestJob.mediaType === 'Video' ? (
          <VideoLibrary sx={{ fontSize: 24, color: 'text.secondary' }} />
        ) : (
          <Photo sx={{ fontSize: 24, color: 'text.secondary' }} />
        )}
      </Avatar>
    );
  };

  return (
    <Box sx={{ p: 1.5 }}>
      {/* Main Media Item Info */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {/* Media Preview */}
        {getMediaPreview()}
        
        {/* Media Details */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          {/* Job Summary - more compact */}
          <Box sx={{ display: 'flex', gap: 0.5, mb: 0.5 }}>
            <Chip
              label={`${jobs.length} job${jobs.length > 1 ? 's' : ''}`}
              size="small"
              variant="outlined"
              sx={{ height: 18, fontSize: '0.6rem' }}
            />
            {completedJobs.length > 0 && (
              <Chip
                label={`${completedJobs.length} done`}
                size="small"
                color="success"
                variant="outlined"
                sx={{ height: 18, fontSize: '0.6rem' }}
              />
            )}
          </Box>

          {/* Latest Job Status */}
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              display: 'block',
              fontSize: '0.7rem'
            }}
            title={latestJob.filename || mediaItem?.filename || `Job ${latestJob.jobId?.slice(-8) || 'Unknown'}`}
          >
            {latestJob.message || latestJob.status || 'Processing'}
          </Typography>

          {/* Progress Bar (for active jobs) */}
          {hasActiveJobs && (
            <LinearProgress
              variant="determinate"
              value={latestJob.progress || 0}
              sx={{
                height: 2,
                borderRadius: 1,
                mt: 0.5,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 1
                }
              }}
            />
          )}
        </Box>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <IconButton
            size="small"
            onClick={() => onClearJob(latestJob.jobId)}
            sx={{ color: 'text.secondary', p: 0.5 }}
            title="Remove job"
          >
            <Close sx={{ fontSize: 16 }} />
          </IconButton>

          {jobs.length > 1 && (
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
              sx={{ color: 'text.secondary', p: 0.5 }}
              title={expanded ? 'Collapse' : 'Show all jobs'}
            >
              {expanded ? <ExpandLess sx={{ fontSize: 16 }} /> : <ExpandMore sx={{ fontSize: 16 }} />}
            </IconButton>
          )}
        </Box>
      </Box>
      
      {/* Expanded Job List */}
      {jobs.length > 1 && (
        <Collapse in={expanded} timeout={200}>
          <Box sx={{ mt: 1.5, pt: 1.5, borderTop: 1, borderColor: 'divider' }}>
            <Typography
              variant="subtitle2"
              sx={{
                color: 'text.secondary',
                mb: 1,
                fontSize: '0.7rem',
                fontWeight: 500
              }}
            >
              All Jobs ({jobs.length})
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              {jobs.map((job) => (
                <Box
                  key={job.jobId}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    py: 0.5,
                    px: 1,
                    borderRadius: 1,
                    backgroundColor: 'grey.50',
                    '&:hover': {
                      backgroundColor: 'grey.100'
                    }
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.65rem',
                      color: 'text.secondary',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      flex: 1,
                      mr: 1
                    }}
                    title={`${job.filename || 'Unknown'} - ${job.message || job.status}`}
                  >
                    {job.message || job.status}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={() => onClearJob(job.jobId)}
                    sx={{ p: 0.25 }}
                    title="Remove job"
                  >
                    <Close sx={{ fontSize: 12 }} />
                  </IconButton>
                </Box>
              ))}
            </Box>
          </Box>
        </Collapse>
      )}
    </Box>
  );
};

export default MediaJobGroup;
