import React from 'react';
import { Button, Box } from '@mui/material';

interface GooglePhotosButtonProps {
  onClick: () => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const GooglePhotosButton: React.FC<GooglePhotosButtonProps> = ({
  onClick,
  disabled = false,
  size = 'medium',
  fullWidth = false
}) => {
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 28;
      default: // medium
        return 24; // Minimum 24dp as per Google guidelines
    }
  };

  const iconSize = getIconSize();

  return (
    <Button
      variant="contained"
      onClick={onClick}
      disabled={disabled}
      fullWidth={fullWidth}
      sx={{
        backgroundColor: '#FFFFFF !important', // Exact white as per Google guidelines
        color: '#3C4043', // Dark gray as per Google guidelines
        textTransform: 'none',
        fontWeight: 500,
        boxShadow: 'none',
        border: '1px solid #e8eaed', // Light gray border for visibility
        '&:hover': {
          backgroundColor: '#FFFFFF !important', // Keep white background on hover
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)',
          borderColor: '#dadce0' // Slightly darker border on hover
        },
        '&:disabled': {
          backgroundColor: '#FFFFFF !important', // Keep white background when disabled
          color: 'action.disabled',
          boxShadow: 'none',
          borderColor: '#f1f3f4'
        },
        borderRadius: 1,
        px: 3, // Match Subscribe button padding
        py: 1.5, // Match Subscribe button padding
        minWidth: 'auto',
        display: 'flex',
        alignItems: 'center',
        gap: '12px' // Proper spacing for icon margins
      }}
    >
      <Box
        component="img"
        src="/Google_Photos_icon_(2020).svg"
        alt="Google Photos"
        sx={{
          width: iconSize,
          height: iconSize,
          flexShrink: 0
        }}
      />
      Load from Google Photos
    </Button>
  );
};

export default GooglePhotosButton;
