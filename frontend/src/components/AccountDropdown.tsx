import React, { useState } from 'react';
import {
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Avatar
} from '@mui/material';
import {
  AccountCircle,
  Logout,
  ExpandMore
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface AccountDropdownProps {
  userEmail: string;
  profilePictureUrl?: string;
  onLogout: () => void;
}

const AccountDropdown: React.FC<AccountDropdownProps> = ({ userEmail, profilePictureUrl, onLogout }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAccountClick = () => {
    handleClose();
    navigate('/account');
  };

  const handleLogoutClick = () => {
    handleClose();
    onLogout();
  };

  // Get initials from email for avatar
  const getInitials = (email: string) => {
    const name = email.split('@')[0];
    return name.charAt(0).toUpperCase();
  };

  return (
    <>
      <Button
        onClick={handleClick}
        endIcon={<ExpandMore />}
        sx={{
          color: 'text.secondary',
          textTransform: 'none',
          fontWeight: 500,
          '&:hover': {
            backgroundColor: 'action.hover',
          },
          borderRadius: 1,
          px: 2,
          py: 1
        }}
      >
        <Avatar
          src={profilePictureUrl}
          sx={{
            width: 32,
            height: 32,
            mr: 1,
            bgcolor: 'primary.main',
            fontSize: '0.875rem'
          }}
        >
          {!profilePictureUrl && getInitials(userEmail)}
        </Avatar>
        {userEmail.split('@')[0]}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'account-button',
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        sx={{
          '& .MuiPaper-root': {
            minWidth: 200,
            mt: 1,
            borderRadius: 2,
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          }
        }}
      >
        <MenuItem onClick={handleAccountClick} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <AccountCircle fontSize="small" />
          </ListItemIcon>
          <ListItemText>Account</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleLogoutClick} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          <ListItemText>Sign out</ListItemText>
        </MenuItem>
      </Menu>
    </>
  );
};

export default AccountDropdown;
