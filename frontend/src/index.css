/* CSS Custom Properties for theme colors */
:root {
  /* Light theme colors */
  --color-background: #ffffff;
  --color-surface: #ffffff;
  --color-text-primary: #202124;
  --color-text-secondary: #5f6368;
  --color-border: #e8eaed;
  --color-primary: #1a73e8;
  --color-primary-hover: #1557b0;
}

[data-theme="dark"] {
  /* Dark theme colors */
  --color-background: #121212;
  --color-surface: #1e1e1e;
  --color-text-primary: #e8eaed;
  --color-text-secondary: #9aa0a6;
  --color-border: #3c4043;
  --color-primary: #4285f4;
  --color-primary-hover: #1a73e8;
}

body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

* {
  box-sizing: border-box;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers with theme support */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* Theme transition for all elements */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Fade-in animation for preview images */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
