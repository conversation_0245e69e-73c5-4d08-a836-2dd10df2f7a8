### Test Compression API Endpoints
### This file contains HTTP requests to test the new compression workflow

@backend_url = http://localhost:5119
@jwt_token = YOUR_JWT_TOKEN_HERE
@media_item_id = YOUR_MEDIA_ITEM_ID_HERE

### 1. Start a compression job
POST {{backend_url}}/api/videos/{{media_item_id}}/compress
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "quality": "medium",
  "uploadToGooglePhotos": true,
  "overwriteOriginal": false
}

### 2. Get compression job status
GET {{backend_url}}/api/videos/compression-jobs/{{$response.jobId}}/status
Authorization: Bearer {{jwt_token}}

### 3. Get all compression jobs for user
GET {{backend_url}}/api/videos/compression-jobs?page=1&pageSize=10
Authorization: Bearer {{jwt_token}}

### 4. Start a high quality compression job (replace original)
POST {{backend_url}}/api/videos/{{media_item_id}}/compress
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "quality": "high",
  "uploadToGooglePhotos": true,
  "overwriteOriginal": true
}

### 5. Compress but keep in cloud storage only (don't upload to Google Photos)
POST {{backend_url}}/api/videos/{{media_item_id}}/compress
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "quality": "medium",
  "uploadToGooglePhotos": false,
  "overwriteOriginal": false
}

### 6. Low quality compression with upload but keep original
POST {{backend_url}}/api/videos/{{media_item_id}}/compress
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "quality": "low",
  "uploadToGooglePhotos": true,
  "overwriteOriginal": false
}

### 5. Cancel a compression job (replace JOB_ID with actual job ID)
POST {{backend_url}}/api/videos/compression-jobs/JOB_ID_HERE/cancel
Authorization: Bearer {{jwt_token}}

### 6. Test with low quality compression
POST {{backend_url}}/api/videos/{{media_item_id}}/compress
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "quality": "low",
  "overwriteOriginal": false
}

### Test Error Scenarios

### 7. Test with invalid media item ID
POST {{backend_url}}/api/videos/invalid-media-item-id/compress
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "quality": "medium",
  "overwriteOriginal": false
}

### 8. Test with invalid quality setting
POST {{backend_url}}/api/videos/{{media_item_id}}/compress
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "quality": "invalid-quality",
  "overwriteOriginal": false
}

### 9. Test without authorization
POST {{backend_url}}/api/videos/{{media_item_id}}/compress
Content-Type: application/json

{
  "quality": "medium",
  "overwriteOriginal": false
}

### 10. Test getting status for non-existent job
GET {{backend_url}}/api/videos/compression-jobs/non-existent-job-id/status
Authorization: Bearer {{jwt_token}}
